import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import 'package:intl/intl.dart';

class EdrPatientDetailsScreen extends StatefulWidget {
  final bool showSearchOption;

  const EdrPatientDetailsScreen({
    super.key,
    this.showSearchOption = true, // Default to true for backward compatibility
  });

  @override
  State<EdrPatientDetailsScreen> createState() =>
      _EdrPatientDetailsScreenState();
}

class _EdrPatientDetailsScreenState extends State<EdrPatientDetailsScreen> {
  bool _complaintsExpanded = true;
  bool _medicalHistoryExpanded = true;
  bool _dentalHistoryExpanded = true;
  bool _examinationExpanded = true;
  bool _treatmentPlanExpanded = true;

  String? _selectedAreaOfComplaint;
  String? _selectedFromTooth;
  String? _selectedToTooth;
  DateTime? _selectedReminderDate;
  TimeOfDay? _selectedReminderTime;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Electronic Dental Records'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Conditionally show search section only when creating new patient
              if (widget.showSearchOption) ...[
                const Text(
                  'Search Patient',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const CustomTextFormField(
                  hintText: 'Search Patient',
                  prefix: Icon(
                    Icons.search,
                  ),
                ),
                const SizedBox(height: 20),
              ],
              const Text(
                'First Name',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter First Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Last Name',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Last Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Patient ID',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Patient ID',
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Age',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '10',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date Of Birth',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '09 Feb 2021',
                          prefix: Icon(
                            Icons.calendar_today,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Gender',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  Radio(
                    value: 'Male',
                    groupValue: 'Male',
                    onChanged: (value) {},
                    activeColor: AppColors.primary,
                  ),
                  const Text('Male'),
                  Radio(
                    value: 'Female',
                    groupValue: 'Male',
                    onChanged: (value) {},
                    activeColor: AppColors.primary,
                  ),
                  const Text('Female'),
                ],
              ),
              const SizedBox(height: 20),
              _buildExpandableSection(
                title: 'Complaints',
                isExpanded: _complaintsExpanded,
                onTap: () {
                  setState(() {
                    _complaintsExpanded = !_complaintsExpanded;
                  });
                },
                child: _buildComplaintsSection(),
              ),
              _buildExpandableSection(
                title: 'Medical History',
                isExpanded: _medicalHistoryExpanded,
                onTap: () {
                  setState(() {
                    _medicalHistoryExpanded = !_medicalHistoryExpanded;
                  });
                },
                child: _buildMedicalHistorySection(),
              ),
              _buildExpandableSection(
                title: 'Dental History',
                isExpanded: _dentalHistoryExpanded,
                onTap: () {
                  setState(() {
                    _dentalHistoryExpanded = !_dentalHistoryExpanded;
                  });
                },
                child: _buildDentalHistorySection(),
              ),
              _buildExpandableSection(
                title: 'Examination',
                isExpanded: _examinationExpanded,
                onTap: () {
                  setState(() {
                    _examinationExpanded = !_examinationExpanded;
                  });
                },
                child: _buildExaminationSection(),
              ),
              _buildExpandableSection(
                title: 'Treatment Plan',
                isExpanded: _treatmentPlanExpanded,
                onTap: () {
                  setState(() {
                    _treatmentPlanExpanded = !_treatmentPlanExpanded;
                  });
                },
                child: _buildTreatmentPlanSection(),
              ),
              const SizedBox(height: 30),
              CustomElevatedButton(
                onPressed: () {},
                text: 'Save',
                buttonStyle: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExpandableSection({
    required String title,
    required bool isExpanded,
    required VoidCallback onTap,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          contentPadding: EdgeInsets.zero,
          title: Row(
            children: [
              Text(title,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold)),
              if (title == 'Complaints' ||
                  title == 'Medical History' ||
                  title == 'Dental History' ||
                  title == 'Examination' ||
                  title == 'Treatment Plan')
                Text(
                  ' *',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
            ],
          ),
          trailing: Icon(
            isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
          ),
          onTap: onTap,
        ),
        if (isExpanded) child,
      ],
    );
  }

  Widget _buildComplaintsSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: [
              Chip(
                  label: const Text('Pain'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                  label: const Text('Swelling'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Discolouration'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Crowned Teeth'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Bad Breathe'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                  label: const Text('Gum Swelling'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
            ],
          ),
          const SizedBox(height: 10),
          const CustomTextFormField(
            hintText: 'Search Complaint',
            prefix: Icon(
              Icons.search,
            ),
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              const Text('From'),
              const SizedBox(width: 10),
              Expanded(
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.remove),
                    ),
                    const Text('10'),
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.add),
                    ),
                    const Text('Days'),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.remove),
                    ),
                    const Text('10'),
                    IconButton(
                      onPressed: () {},
                      icon: const Icon(Icons.add),
                    ),
                    const Text('Weeks'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          const Text('Area of Complaint', style: TextStyle()),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedAreaOfComplaint,
            decoration: InputDecoration(
              hintText: 'Select Area of Complaint',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              fillColor: AppColors.lightGrey,
              filled: true,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: const [
              DropdownMenuItem(
                  value: 'Upper anterior', child: Text('Upper anterior')),
              DropdownMenuItem(
                  value: 'Lower anterior', child: Text('Lower anterior')),
              DropdownMenuItem(
                  value: 'Left upper posterior',
                  child: Text('Left upper posterior')),
              DropdownMenuItem(
                  value: 'Right upper posterior',
                  child: Text('Right upper posterior')),
              DropdownMenuItem(
                  value: 'Left lower posterior',
                  child: Text('Left lower posterior')),
              DropdownMenuItem(
                  value: 'Right lower posterior',
                  child: Text('Right lower posterior')),
            ],
            onChanged: (String? newValue) {
              setState(() {
                _selectedAreaOfComplaint = newValue;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMedicalHistorySection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: [
              Chip(
                  label: const Text('Cardiac'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                  label: const Text('Neuro'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                  label: const Text('CAD'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Angioplasty'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Ortho'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                  label: const Text('Hypertension'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Open Heart Surgery'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                  label: const Text('Angiogram'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Nephro'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Gastro'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Cardiac'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Respiratory'),
                backgroundColor: AppColors.primary2,
              ),
            ],
          ),
          const SizedBox(height: 10),
          const CustomTextFormField(
            hintText: 'Search Drugs',
            prefix: Icon(
              Icons.search,
            ),
            suffix: Icon(
              Icons.arrow_drop_down,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDentalHistorySection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: [
              Chip(
                  label: const Text('Root canal'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                  label: const Text('Bridge'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Crown'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Draling'),
                backgroundColor: AppColors.primary2,
              ),
            ],
          ),
          const SizedBox(height: 10),
          const CustomTextFormField(
            hintText: 'Search Dental History terms',
            prefix: Icon(
              Icons.search,
            ),
            suffix: Icon(
              Icons.arrow_drop_down,
            ),
          ),
          const SizedBox(height: 10),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('From Tooth', style: TextStyle()),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedFromTooth,
                      decoration: InputDecoration(
                        hintText: 'Select Tooth',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide.none,
                        ),
                        fillColor: AppColors.lightGrey,
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items:
                          List.generate(32, (index) => (index + 1).toString())
                              .map((String tooth) {
                        return DropdownMenuItem(
                          value: tooth,
                          child: Text(tooth),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedFromTooth = newValue;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('To Tooth', style: TextStyle()),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedToTooth,
                      decoration: InputDecoration(
                        hintText: 'Select Tooth',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide.none,
                        ),
                        fillColor: AppColors.lightGrey,
                        filled: true,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items:
                          List.generate(32, (index) => (index + 1).toString())
                              .map((String tooth) {
                        return DropdownMenuItem(
                          value: tooth,
                          child: Text(tooth),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          _selectedToTooth = newValue;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          const Text('Remarks', style: TextStyle()),
          const SizedBox(height: 8),
          const CustomTextFormField(
            hintText: 'Enter Remarks',
          ),
        ],
      ),
    );
  }

  Widget _buildExaminationSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: [
              Chip(
                label: const Text('Caries'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                  label: const Text('Swelling'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Crown'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Bridge'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                  label: const Text('Stains'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Impact'),
                backgroundColor: AppColors.primary2,
              ),
            ],
          ),
          const SizedBox(height: 10),
          const CustomTextFormField(
            hintText: 'Search Examinations',
            prefix: Icon(
              Icons.search,
            ),
            suffix: Icon(
              Icons.arrow_drop_down,
            ),
          ),
          const SizedBox(height: 10),
          const Text('Tooth Selection', style: TextStyle()),
          const SizedBox(height: 8),
          const CustomTextFormField(
            hintText: 'Search Complaint',
            suffix: Icon(
              Icons.arrow_drop_down,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTreatmentPlanSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: [
              Chip(
                  label: const Text('Root canal'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                  label: const Text('Bridge'),
                  onDeleted: () {},
                  deleteIconColor: AppColors.primary,
                  backgroundColor: AppColors.primary2),
              Chip(
                label: const Text('Crown'),
                backgroundColor: AppColors.primary2,
              ),
              Chip(
                label: const Text('Draling'),
                backgroundColor: AppColors.primary2,
              ),
            ],
          ),
          const SizedBox(height: 10),
          const CustomTextFormField(
            hintText: 'Search Treatment Plan',
            prefix: Icon(
              Icons.search,
            ),
            suffix: Icon(
              Icons.arrow_drop_down,
            ),
          ),
          const SizedBox(height: 10),
          const Text('Schedule Appointment', style: TextStyle()),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    final TimeOfDay? picked = await showTimePicker(
                      context: context,
                      initialTime: _selectedReminderTime ?? TimeOfDay.now(),
                    );
                    if (picked != null && picked != _selectedReminderTime) {
                      setState(() {
                        _selectedReminderTime = picked;
                      });
                    }
                  },
                  child: AbsorbPointer(
                    child: CustomTextFormField(
                      hintText: _selectedReminderTime == null
                          ? '10:24 AM'
                          : _selectedReminderTime!.format(context),
                      prefix: const Icon(
                        Icons.access_time,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: GestureDetector(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _selectedReminderDate ?? DateTime.now(),
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2101),
                    );
                    if (picked != null && picked != _selectedReminderDate) {
                      setState(() {
                        _selectedReminderDate = picked;
                      });
                    }
                  },
                  child: AbsorbPointer(
                    child: CustomTextFormField(
                      hintText: _selectedReminderDate == null
                          ? '09 Feb 2021'
                          : DateFormat('dd MMM yyyy')
                              .format(_selectedReminderDate!),
                      prefix: const Icon(
                        Icons.calendar_today,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          const Text('Reminder Text', style: TextStyle()),
          const SizedBox(height: 8),
          const CustomTextFormField(
            hintText: 'Enter Reminder Text',
          ),
        ],
      ),
    );
  }
}
