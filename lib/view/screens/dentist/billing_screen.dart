import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/create_billing_screen.dart';

class BillingScreen extends StatelessWidget {
  const BillingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    return Visibility(
      visible: permissionService.hasAnyPermission('billing', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Billing'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Patient Billing',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                Visibility(
                  visible: permissionService.hasPermission('billing', 'is_list'),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columnSpacing: 10,
                      horizontalMargin: 10,
                      headingRowColor: WidgetStateProperty.all(AppColors.primary),
                      headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      columns: const [
                        DataColumn(label: Text('S.No')),
                        DataColumn(label: Text('Patient Name')),
                        DataColumn(label: Text('Service')),
                        DataColumn(label: Text('Amount')),
                        DataColumn(label: Text('Date')),
                        DataColumn(label: Text('Edit')),
                        DataColumn(label: Text('Delete')),
                      ],
                      rows: [
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            const DataCell(Text('1')),
                            const DataCell(Text('Matthew Thomas')),
                            const DataCell(Text('Root Canal')),
                            const DataCell(Text('₹5,000')),
                            const DataCell(Text('30/10/2020')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Matthew Thomas?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Matthew Thomas deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(Colors.white),
                          cells: [
                            const DataCell(Text('2')),
                            const DataCell(Text('John Davis')),
                            const DataCell(Text('Dental Cleaning')),
                            const DataCell(Text('₹2,500')),
                            const DataCell(Text('16/10/2021')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for John Davis?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for John Davis deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            const DataCell(Text('3')),
                            const DataCell(Text('Elizabeth Jones')),
                            const DataCell(Text('Crown Placement')),
                            const DataCell(Text('₹8,000')),
                            const DataCell(Text('10/01/2024')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Elizabeth Jones?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Elizabeth Jones deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(Colors.white),
                          cells: [
                            const DataCell(Text('4')),
                            const DataCell(Text('Ryan Young')),
                            const DataCell(Text('Tooth Extraction')),
                            const DataCell(Text('₹1,500')),
                            const DataCell(Text('09/01/2022')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Ryan Young?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Ryan Young deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            const DataCell(Text('5')),
                            const DataCell(Text('Anthony Moore')),
                            const DataCell(Text('Dental Implant')),
                            const DataCell(Text('₹15,000')),
                            const DataCell(Text('30/01/2025')),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(showSearchOption: false));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Anthony Moore?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Anthony Moore deleted successfully!');
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('billing', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreateBillingScreen(showSearchOption: true));
            },
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }
}
