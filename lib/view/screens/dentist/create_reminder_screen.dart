import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/custom_text_form_field.dart';
import 'package:platix/view/widgets/textfield_label.dart';

class CreateReminderScreen extends StatelessWidget {
  const CreateReminderScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Reminder'),
      ),
      body: SingleChildScrollView( 
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const TextFieldLabel(
                label: 'Search Patient',
                isMandatory: true,
              ),
              const CustomTextFormField(
                hintText: 'Search Patient',
                prefix: Icon(Icons.search),
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'First Name',
                isMandatory: false,
              ),
              const CustomTextFormField(
                hintText: 'Enter First Name',
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Last Name',
                isMandatory: false,
              ),
              const CustomTextFormField(
                hintText: 'Enter Last Name',
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Mobile Number',
                isMandatory: false,
              ),
              const CustomTextFormField(
                hintText: '+91 XXXXXXXXXX',
                prefix: Icon(Icons.phone),
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Select Reminder',
                isMandatory: true,
              ),
              Row(
                children: [
                  Expanded(
                    child: CustomTextFormField(
                      hintText: '10:24 AM',
                      prefix: const Icon(Icons.access_time),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: CustomTextFormField(
                      hintText: '09 Feb 2021',
                      prefix: const Icon(Icons.calendar_today),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const TextFieldLabel(
                label: 'Reminder Text',
                isMandatory: true,
              ),
              const CustomTextFormField(
                hintText: 'what should I remind you on ?',
                maxLines: 5,
              ),
              const SizedBox(height: 30),
              CustomElevatedButton(
                onPressed: () {},
                text: 'Save',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
