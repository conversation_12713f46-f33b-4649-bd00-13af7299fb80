import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/data_store.dart';
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/controllers/dentist_controllers/store_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/signinOption_screen.dart';
import 'package:badges/badges.dart' as badges;
import 'package:platix/view/screens/cart_screen.dart';
import 'package:intl/intl.dart'; // Added import
import '../../../controllers/dentist_controllers/dentist_search_controller.dart';
import '../../../data/models/dentist/dentist_search_model.dart';
import '../../../data/models/user_model.dart';
import '../../../utils/web_responsive_utils.dart';
import '../notification_screen.dart';
import 'dentist_service_details_screen.dart';
import 'item_details_screen.dart';
import '../store_homescreen.dart';
import '../../widgets/custom_search_dropdown.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:platix/utils/constants/colors.dart';
import '../../widgets/dentist_drawer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  HomeController homeController = Get.put(HomeController());
  final DentistSearchController searchControllerGet =
      Get.put(DentistSearchController());
  final StoreController storeController = Get.put(StoreController());
  final PermissionService _permissionService = PermissionService();

  List<Organization1> searchResults = [];
  bool isSearching = false;
  bool isVerifyingEmail = false;
  final UserRecord? userRecord = getData.read('userRecord') == null
      ? null
      : UserRecord.fromJson(getData.read('userRecord'));
  String? token = getData.read('token');

  // Date variables for Revenue and Cost sections
  DateTime? fromDateRevenue;
  DateTime? toDateRevenue;
  DateTime? fromDateCost;
  DateTime? toDateCost;
  DateTime? fromDateServices; // Added for Services section
  DateTime? toDateServices; // Added for Services section

  @override
  void initState() {
    super.initState();
    // Initialize dates to current date
    fromDateRevenue = DateTime.now();
    toDateRevenue = DateTime.now();
    fromDateCost = DateTime.now();
    toDateCost = DateTime.now();
    fromDateServices = DateTime.now(); // Initialize for Services
    toDateServices = DateTime.now(); // Initialize for Services
  }

  // Function to show date picker
  Future<void> _selectDate(
      BuildContext context, Function(DateTime) onDateSelected,
      {DateTime? initialDate, DateTime? firstDate, DateTime? lastDate}) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: AppColors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  List<Map<String, dynamic>> _getDummyServicesData() {
    // Replace with your actual data fetching logic
    return [
      {'service_name': 'Cleaning'},
      {'service_name': 'Whitening'},
      {'service_name': 'Extraction'},
      {'service_name': 'Filling'},
      {'service_name': 'Crown'},
      {'service_name': 'Cleaning'},
      {'service_name': 'Whitening'},
      {'service_name': 'Cleaning'},
      {'service_name': 'Root Canal'},
      {'service_name': 'Whitening'},
      {'service_name': 'Crown'},
      {'service_name': 'Filling'},
      {'service_name': 'Cleaning'},
    ];
  }

  List<Map<String, dynamic>> _processServicesData(
      List<Map<String, dynamic>> data) {
    final Map<String, int> serviceCounts = {};

    for (var item in data) {
      final serviceName = item['service_name'];
      serviceCounts[serviceName] = (serviceCounts[serviceName] ?? 0) + 1;
    }

    final sortedServices = serviceCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedServices
        .take(5)
        .map((entry) => {'name': entry.key, 'count': entry.value})
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    // Process data for the chart
    final servicesData = _getDummyServicesData();
    final topServices = _processServicesData(servicesData);

    return GetBuilder<CartController>(
      init: CartController(),
      builder: (cartController) {
        return Visibility(
          visible: _permissionService.hasAnyPermission('dashboards', ['is_view', 'is_list']),
          child: Scaffold(
            appBar: AppBar(
              title: const Text('Dashboard'),
            ),
            body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'DASHBOARDS',
                      style: CustomTextStyles.b4_1
                          .copyWith(color: AppColors.primary),
                    ),
                  ),
                  const SizedBox(height: AppSizes.md),
                  // REVENUE Section
                  Visibility(
                    visible: _permissionService.hasPermission('dashboards', 'is_list'),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'REVENUE',
                                style: CustomTextStyles.b4_1,
                              ),
                              Row(
                                children: [
                                  GestureDetector(
                                    onTap: () => _selectDate(context, (date) {
                                      setState(() {
                                        fromDateRevenue = date;
                                      });
                                    }, initialDate: fromDateRevenue),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppSizes.sm,
                                          vertical: AppSizes.xs),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary),
                                        borderRadius:
                                            BorderRadius.circular(AppSizes.xs),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.calendar_today,
                                              size: 16, color: AppColors.primary),
                                          const SizedBox(width: AppSizes.xs),
                                          Text(
                                            fromDateRevenue != null
                                                ? DateFormat('dd MMM yyyy')
                                                    .format(fromDateRevenue!)
                                                : 'Select Date',
                                            style: CustomTextStyles.b6_3,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: AppSizes.sm),
                                  GestureDetector(
                                    onTap: () => _selectDate(context, (date) {
                                      setState(() {
                                        toDateRevenue = date;
                                      });
                                    }, initialDate: toDateRevenue),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppSizes.sm,
                                          vertical: AppSizes.xs),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary),
                                        borderRadius:
                                            BorderRadius.circular(AppSizes.xs),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.calendar_today,
                                              size: 16, color: AppColors.primary),
                                          const SizedBox(width: AppSizes.xs),
                                          Text(
                                            toDateRevenue != null
                                                ? DateFormat('dd MMM yyyy')
                                                    .format(toDateRevenue!)
                                                : 'Select Date',
                                            style: CustomTextStyles.b6_3,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: AppSizes.sm),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          padding: const EdgeInsets.all(AppSizes.md),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: AppDecoration.shadow1_3,
                            borderRadius: BorderRadiusStyle.radius8,
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.xs),
                                      Text('Label 1', style: CustomTextStyles.b6_3),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary5,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.xs),
                                      Text('Label 2', style: CustomTextStyles.b6_3),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary3,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.xs),
                                      Text('Label 3', style: CustomTextStyles.b6_3),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: AppSizes.md),
                              SizedBox(
                                height: 220,
                                child: PieChart(
                                  PieChartData(
                                    sections: [
                                      PieChartSectionData(
                                        color: AppColors.primary,
                                        value: 40,
                                        title: '',
                                        radius: 70,
                                      ),
                                      PieChartSectionData(
                                        color: AppColors.primary5,
                                        value: 30,
                                        title: '',
                                        radius: 70,
                                      ),
                                      PieChartSectionData(
                                        color: AppColors.primary4,
                                        value: 30,
                                        title: '',
                                        radius: 70,
                                      ),
                                    ],
                                    sectionsSpace: 0,
                                    centerSpaceRadius: 35,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                  // SERVICES Section
                  Visibility(
                    visible: _permissionService.hasPermission('dashboards', 'is_list'),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'SERVICES',
                                style: CustomTextStyles.b4_1,
                              ),
                              Row(
                                children: [
                                  GestureDetector(
                                    onTap: () => _selectDate(context, (date) {
                                      setState(() {
                                        fromDateServices = date;
                                      });
                                    }, initialDate: fromDateServices),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppSizes.sm,
                                          vertical: AppSizes.xs),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary),
                                        borderRadius:
                                            BorderRadius.circular(AppSizes.xs),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.calendar_today,
                                              size: 16, color: AppColors.primary),
                                          const SizedBox(width: AppSizes.xs),
                                          Text(
                                            fromDateServices != null
                                                ? DateFormat('dd MMM yyyy')
                                                    .format(fromDateServices!)
                                                : 'Select Date',
                                            style: CustomTextStyles.b6_3,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: AppSizes.sm),
                                  GestureDetector(
                                    onTap: () => _selectDate(context, (date) {
                                      setState(() {
                                        toDateServices = date;
                                      });
                                    }, initialDate: toDateServices),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppSizes.sm,
                                          vertical: AppSizes.xs),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary),
                                        borderRadius:
                                            BorderRadius.circular(AppSizes.xs),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.calendar_today,
                                              size: 16, color: AppColors.primary),
                                          const SizedBox(width: AppSizes.xs),
                                          Text(
                                            toDateServices != null
                                                ? DateFormat('dd MMM yyyy')
                                                    .format(toDateServices!)
                                                : 'Select Date',
                                            style: CustomTextStyles.b6_3,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: AppSizes.sm),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          padding: const EdgeInsets.all(AppSizes.md),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: AppDecoration.shadow1_3,
                            borderRadius: BorderRadiusStyle.radius8,
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.xs),
                                      Text('Top 5 Services',
                                          style: CustomTextStyles.b6_3),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: AppSizes.md),
                              SizedBox(
                                height: 220,
                                child: BarChart(
                                  BarChartData(
                                    barGroups: topServices.asMap().entries.map((entry) {
                                      final index = entry.key;
                                      final service = entry.value;
                                      return BarChartGroupData(
                                        x: index,
                                        barRods: [
                                          BarChartRodData(
                                            toY: service['count'].toDouble(),
                                            color: AppColors.primary5,
                                            width: 15,
                                          ),
                                        ],
                                      );
                                    }).toList(),
                                    titlesData: FlTitlesData(
                                      show: true,
                                      bottomTitles: AxisTitles(
                                        sideTitles: SideTitles(
                                          showTitles: true,
                                          getTitlesWidget: (value, meta) {
                                            final index = value.toInt();
                                            if (index >= 0 &&
                                                index < topServices.length) {
                                              return SideTitleWidget(
                                                axisSide: meta.axisSide,
                                                angle: -0.785, // 45 degrees in radians
                                                child: Text(
                                                  topServices[index]['name'],
                                                  style: CustomTextStyles.b6_3.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              );
                                            }
                                            return const Text('');
                                          },
                                          reservedSize: 60,
                                        ),
                                      ),
                                      leftTitles: AxisTitles(
                                        sideTitles: SideTitles(
                                          showTitles: true,
                                          getTitlesWidget: (value, meta) {
                                            return Text(
                                              '${value.toInt()}',
                                              style: CustomTextStyles.b6_3,
                                            );
                                          },
                                          reservedSize: 30,
                                        ),
                                        axisNameWidget: Text(
                                          'Number of Services',
                                          style: CustomTextStyles.b6_3,
                                        ),
                                      ),
                                      topTitles: const AxisTitles(
                                        sideTitles: SideTitles(showTitles: false),
                                      ),
                                      rightTitles: const AxisTitles(
                                        sideTitles: SideTitles(showTitles: false),
                                      ),
                                    ),
                                    borderData: FlBorderData(
                                      show: true,
                                      border: Border.all(
                                          color: AppColors.lightGrey, width: 1),
                                    ),
                                    gridData: FlGridData(
                                      show: true,
                                      drawVerticalLine: false,
                                      getDrawingHorizontalLine: (value) => FlLine(
                                        color: AppColors.lightGrey,
                                        strokeWidth: 1,
                                      ),
                                    ),
                                  ),
                                  swapAnimationDuration:
                                      const Duration(milliseconds: 150),
                                  swapAnimationCurve: Curves.linear,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                  // PATIENTS Section (New)
                  Visibility(
                    visible: _permissionService.hasPermission('dashboards', 'is_list'),
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'PATIENTS',
                                style: CustomTextStyles.b4_1,
                              ),
                              Row(
                                children: [
                                  GestureDetector(
                                    onTap: () => _selectDate(context, (date) {
                                      setState(() {
                                        // Add state variable for fromDatePatients
                                      });
                                    }, initialDate: DateTime.now()),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppSizes.sm,
                                          vertical: AppSizes.xs),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary),
                                        borderRadius:
                                            BorderRadius.circular(AppSizes.xs),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.calendar_today,
                                              size: 16, color: AppColors.primary),
                                          const SizedBox(width: AppSizes.xs),
                                          Text(
                                            DateFormat('dd MMM yyyy').format(DateTime
                                                .now()), // Use current date for now
                                            style: CustomTextStyles.b6_3,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: AppSizes.sm),
                                  GestureDetector(
                                    onTap: () => _selectDate(context, (date) {
                                      setState(() {
                                        // Add state variable for toDatePatients
                                      });
                                    }, initialDate: DateTime.now()),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppSizes.sm,
                                          vertical: AppSizes.xs),
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary),
                                        borderRadius:
                                            BorderRadius.circular(AppSizes.xs),
                                      ),
                                      child: Row(
                                        children: [
                                          const Icon(Icons.calendar_today,
                                              size: 16, color: AppColors.primary),
                                          const SizedBox(width: AppSizes.xs),
                                          Text(
                                            DateFormat('dd MMM yyyy').format(DateTime
                                                .now()), // Use current date for now
                                            style: CustomTextStyles.b6_3,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: AppSizes.sm),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          padding: const EdgeInsets.all(AppSizes.md),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: AppDecoration.shadow1_3,
                            borderRadius: BorderRadiusStyle.radius8,
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.xs),
                                      Text('New Patients',
                                          style: CustomTextStyles.b6_3),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary5,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.xs),
                                      Text('Returning Patients',
                                          style: CustomTextStyles.b6_3),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: AppSizes.md),
                              SizedBox(
                                height: 220,
                                child: PieChart(
                                  PieChartData(
                                    sections: [
                                      PieChartSectionData(
                                        color: AppColors.primary,
                                        value: 60,
                                        title: '',
                                        radius: 70,
                                      ),
                                      PieChartSectionData(
                                        color: AppColors.primary5,
                                        value: 40,
                                        title: '',
                                        radius: 70,
                                      ),
                                    ],
                                    sectionsSpace: 0,
                                    centerSpaceRadius: 35,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
