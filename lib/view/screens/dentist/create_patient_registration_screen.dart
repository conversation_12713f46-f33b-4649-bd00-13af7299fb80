import 'package:platix/services/permission_service.dart';
import 'package:platix/view/widgets/custom_select_state.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:intl/intl.dart';

class CreatePatientRegistrationScreen extends StatefulWidget {
  final bool showSearchOption;
  
  const CreatePatientRegistrationScreen({
    super.key,
    this.showSearchOption = true,
  });

  @override
  State<CreatePatientRegistrationScreen> createState() =>
      _CreatePatientRegistrationScreenState();
}

class _CreatePatientRegistrationScreenState extends State<CreatePatientRegistrationScreen> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController mobileNumController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  String? _selectedGender;
  DateTime? _selectedDateOfBirth;
  TextEditingController state = TextEditingController();
  TextEditingController city = TextEditingController();
  String country = "India";
  final PermissionService _permissionService = PermissionService();

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    mobileNumController.dispose();
    emailController.dispose();
    ageController.dispose();
    addressController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.showSearchOption ? 'New Patient Registration' : 'Edit Patient Registration'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showSearchOption)
              Visibility(
                visible: _permissionService.hasPermission('Regitration', 'is_list'),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Search Patient',
                          style: CustomTextStyles.b4_1,
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LabelTextField(
                      label: '',
                      hint: 'Search Patient',
                      controller: TextEditingController(),
                      suffix: const Icon(Icons.search),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            const SizedBox(height: 20),
            LabelTextField(
              label: 'First Name',
              hint: 'Enter First Name',
              controller: firstNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Last Name',
              hint: 'Enter Last Name',
              controller: lastNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Mobile Num',
              hint: 'Enter Mobile Number',
              controller: mobileNumController,
              inputType: TextInputType.phone,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Email',
              hint: 'Enter Email',
              controller: emailController,
              inputType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 15),
            Text(
              'Gender',
              style: CustomTextStyles.b4_1,
            ),
            Row(
              children: [
                Radio<String>(
                  value: 'M',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const Text('M'),
                Radio<String>(
                  value: 'F',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const Text('F'),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: LabelTextField(
                    label: 'Age',
                    hint: 'Enter Age',
                    controller: ageController,
                    inputType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context),
                    child: AbsorbPointer(
                      child: LabelTextField(
                        label: 'DOB.',
                        hint: _selectedDateOfBirth == null
                            ? 'Select Date'
                            : DateFormat('dd/MM/yyyy')
                                .format(_selectedDateOfBirth!),
                        controller: TextEditingController(
                            text: _selectedDateOfBirth == null
                                ? ''
                                : DateFormat('dd/MM/yyyy')
                                    .format(_selectedDateOfBirth!)),
                        suffix: const Icon(Icons.calendar_today),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Address',
              hint: 'Enter Address',
              controller: addressController,
              maxLines: 3,
            ),
            const SizedBox(height: 15),
            CustomSelectState(
              spacing: 20.0,
              defaultCountry: "🇮🇳    India",
              onCountryChanged: (value) {
                setState(() {
                  country = value;
                });
              },
              onStateChanged: (value) {
                setState(() {
                  state.text = value;
                });
              },
              onCityChanged: (value) {
                setState(() {
                  city.text = value;
                });
              },
            ),
            const SizedBox(height: 30),
            CustomElevatedButton(
              onPressed: () {
                Get.snackbar('Success', 'Patient Registered Successfully!');
              },
              text: 'Save',
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
