import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/edr_patient_details_screen.dart';
import 'package:platix/view/screens/dentist/patient_registration_screen.dart';

class EdrPatientListScreen extends StatelessWidget {
  const EdrPatientListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PermissionService _permissionService = PermissionService();
    
    // Debug: Print permission status for troubleshooting
    print('🔍 EDR Permission Check:');
    print('  - is_view: ${_permissionService.hasPermission('edr', 'is_view')}');
    print('  - is_list: ${_permissionService.hasPermission('edr', 'is_list')}');
    print('  - is_add: ${_permissionService.hasPermission('edr', 'is_add')}');
    print('  - is_edit: ${_permissionService.hasPermission('edr', 'is_edit')}');
    print('  - is_delete: ${_permissionService.hasPermission('edr', 'is_delete')}');

    return Visibility(
      visible: _permissionService.hasAnyPermission('edr', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Electronic Dental Records'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Visibility(
                  visible: _permissionService.hasPermission('edr', 'is_add'),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Search Patient',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      const TextField(
                        decoration: InputDecoration(
                          hintText: 'Search Patient',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Most Recent Patients',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Visibility(
                  visible: _permissionService.hasPermission('edr', 'is_list'),
                  child: _buildPatientTable(_permissionService),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: _permissionService.hasPermission('edr', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const EdrPatientDetailsScreen(showSearchOption: true));
            },
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }

  // DataTable layout with horizontal scrolling for all screen sizes
  Widget _buildPatientTable(PermissionService permissionService) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        showCheckboxColumn: false,
        columnSpacing: 20,
        horizontalMargin: 10,
        headingRowColor: MaterialStateProperty.all(AppColors.primary),
        headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        columns: const [
          DataColumn(label: Text('S.No')),
          DataColumn(label: Text('Patient Name')),
          DataColumn(label: Text('Age/Gender')),
          DataColumn(label: Text('Date')),
          DataColumn(label: Text('Edit')),
          DataColumn(label: Text('Delete')),
        ],
        rows: [
          DataRow(
            color: MaterialStateProperty.all(AppColors.primary2),
            cells: [
              const DataCell(Text('1')),
              const DataCell(Text('Matthew Thomas')),
              const DataCell(Text('49Yrs/Male')),
              const DataCell(Text('30/10/2020')),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_edit'),
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: AppColors.primary),
                    onPressed: () => Get.to(() => const EdrPatientDetailsScreen(showSearchOption: false)),
                  ),
                ),
              ),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_delete'),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.primary),
                    onPressed: () {
                      Get.defaultDialog(
                        title: 'Delete Patient',
                        titleStyle: const TextStyle(
                          color: AppColors.primary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        middleText: 'Are you sure you want to delete Matthew Thomas?',
                        middleTextStyle: const TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                        ),
                        backgroundColor: AppColors.white,
                        radius: 12,
                        textConfirm: 'Delete',
                        textCancel: 'Cancel',
                        confirmTextColor: Colors.white,
                        cancelTextColor: AppColors.primary,
                        buttonColor: AppColors.primary,
                        onConfirm: () {
                          Get.back();
                          Get.snackbar('Success', 'Patient Matthew Thomas deleted successfully!');
                        },
                        onCancel: () {
                          Get.back();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          DataRow(
            color: MaterialStateProperty.all(Colors.white),
            cells: [
              const DataCell(Text('2')),
              const DataCell(Text('John Davis')),
              const DataCell(Text('89Yrs/Male')),
              const DataCell(Text('16/10/2021')),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_edit'),
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: AppColors.primary),
                    onPressed: () => Get.to(() => const EdrPatientDetailsScreen(showSearchOption: false)),
                  ),
                ),
              ),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_delete'),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.primary),
                    onPressed: () {
                      Get.defaultDialog(
                        title: 'Delete Patient',
                        titleStyle: const TextStyle(
                          color: AppColors.primary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        middleText: 'Are you sure you want to delete John Davis?',
                        middleTextStyle: const TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                        ),
                        backgroundColor: AppColors.white,
                        radius: 12,
                        textConfirm: 'Delete',
                        textCancel: 'Cancel',
                        confirmTextColor: Colors.white,
                        cancelTextColor: AppColors.primary,
                        buttonColor: AppColors.primary,
                        onConfirm: () {
                          Get.back();
                          Get.snackbar('Success', 'Patient John Davis deleted successfully!');
                        },
                        onCancel: () {
                          Get.back();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          // Add remaining 3 patient rows
          DataRow(
            color: MaterialStateProperty.all(AppColors.primary2),
            cells: [
              const DataCell(Text('3')),
              const DataCell(Text('Elizabeth Jones')),
              const DataCell(Text('27Yrs/Female')),
              const DataCell(Text('10/01/2024')),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_edit'),
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: AppColors.primary),
                    onPressed: () => Get.to(() => const EdrPatientDetailsScreen(showSearchOption: false)),
                  ),
                ),
              ),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_delete'),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.primary),
                    onPressed: () {
                      Get.defaultDialog(
                        title: 'Delete Patient',
                        titleStyle: const TextStyle(
                          color: AppColors.primary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        middleText: 'Are you sure you want to delete Elizabeth Jones?',
                        middleTextStyle: const TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                        ),
                        backgroundColor: AppColors.white,
                        radius: 12,
                        textConfirm: 'Delete',
                        textCancel: 'Cancel',
                        confirmTextColor: Colors.white,
                        cancelTextColor: AppColors.primary,
                        buttonColor: AppColors.primary,
                        onConfirm: () {
                          Get.back();
                          Get.snackbar('Success', 'Patient Elizabeth Jones deleted successfully!');
                        },
                        onCancel: () {
                          Get.back();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          DataRow(
            color: MaterialStateProperty.all(Colors.white),
            cells: [
              const DataCell(Text('4')),
              const DataCell(Text('Ryan Young')),
              const DataCell(Text('48Yrs/Male')),
              const DataCell(Text('09/01/2022')),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_edit'),
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: AppColors.primary),
                    onPressed: () => Get.to(() => const EdrPatientDetailsScreen(showSearchOption: false)),
                  ),
                ),
              ),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_delete'),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.primary),
                    onPressed: () {
                      Get.defaultDialog(
                        title: 'Delete Patient',
                        titleStyle: const TextStyle(
                          color: AppColors.primary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        middleText: 'Are you sure you want to delete Ryan Young?',
                        middleTextStyle: const TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                        ),
                        backgroundColor: AppColors.white,
                        radius: 12,
                        textConfirm: 'Delete',
                        textCancel: 'Cancel',
                        confirmTextColor: Colors.white,
                        cancelTextColor: AppColors.primary,
                        buttonColor: AppColors.primary,
                        onConfirm: () {
                          Get.back();
                          Get.snackbar('Success', 'Patient Ryan Young deleted successfully!');
                        },
                        onCancel: () {
                          Get.back();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          DataRow(
            color: MaterialStateProperty.all(AppColors.primary2),
            cells: [
              const DataCell(Text('5')),
              const DataCell(Text('Anthony Moore')),
              const DataCell(Text('38Yrs/Female')),
              const DataCell(Text('30/01/2025')),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_edit'),
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: AppColors.primary),
                    onPressed: () => Get.to(() => const EdrPatientDetailsScreen(showSearchOption: false)),
                  ),
                ),
              ),
              DataCell(
                Visibility(
                  visible: permissionService.hasPermission('edr', 'is_delete'),
                  child: IconButton(
                    icon: const Icon(Icons.delete, color: AppColors.primary),
                    onPressed: () {
                      Get.defaultDialog(
                        title: 'Delete Patient',
                        titleStyle: const TextStyle(
                          color: AppColors.primary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        middleText: 'Are you sure you want to delete Anthony Moore?',
                        middleTextStyle: const TextStyle(
                          color: AppColors.black,
                          fontSize: 14,
                        ),
                        backgroundColor: AppColors.white,
                        radius: 12,
                        textConfirm: 'Delete',
                        textCancel: 'Cancel',
                        confirmTextColor: Colors.white,
                        cancelTextColor: AppColors.primary,
                        buttonColor: AppColors.primary,
                        onConfirm: () {
                          Get.back();
                          Get.snackbar('Success', 'Patient Anthony Moore deleted successfully!');
                        },
                        onCancel: () {
                          Get.back();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
