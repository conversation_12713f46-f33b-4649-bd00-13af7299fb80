import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/data_store.dart';
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/controllers/dentist_controllers/store_controller.dart';
import 'package:platix/controllers/roleselection_controller.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/item_card.dart';
import 'package:platix/view/screens/dentist/dentist_bottombar.dart';
import 'package:platix/view/screens/dentist/dentist_orders_screen.dart';
import 'package:platix/view/screens/dentist/dentist_profilescreen.dart';
import 'package:platix/view/screens/dentist/dentist_reports_screen.dart';
import 'package:platix/view/screens/dentist/dentist_service_screen.dart';
import 'package:platix/view/screens/signinOption_screen.dart';
import 'package:badges/badges.dart' as badges;
import 'package:platix/view/screens/cart_screen.dart';
import '../../../controllers/dentist_controllers/dentist_search_controller.dart';
import '../../../data/models/dentist/dentist_search_model.dart';
import '../../../data/models/user_model.dart';
import '../../../utils/web_responsive_utils.dart';
import '../notification_screen.dart';
import 'dentist_service_details_screen.dart';
import 'item_details_screen.dart';
import '../store_homescreen.dart';
import '../../widgets/dentist_drawer.dart';

class DentistHomeScreen extends StatefulWidget {
  const DentistHomeScreen({super.key});

  @override
  State<DentistHomeScreen> createState() => _DentistHomeScreenState();
}

class _DentistHomeScreenState extends State<DentistHomeScreen> {

  HomeController homeController = Get.put(HomeController());
  final DentistSearchController searchControllerGet = Get.put(DentistSearchController());
  final StoreController storeController = Get.put(StoreController());

  List<Organization1> searchResults = [];
  bool isSearching = false;
  bool isVerifyingEmail = false;
  final UserRecord? userRecord = getData.read('userRecord') == null ? null : UserRecord.fromJson(getData.read('userRecord'));
  String? token = getData.read('token');
  String? userRole = getData.read('userRecord')?['role'];

  @override
  void initState() {
    super.initState();
    log("User Role: $userRole");
    initialize();
  }

  Future<void> initialize() async {
    if (homeController.dentistHomeModel == null) {
      (token == null || token == '') ? null : await homeController.getDashboard();
      await homeController.getDashboardData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CartController>(
      init: CartController(),
      builder: (cartController) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: kIsWeb
          ? WebResponsiveUtils.dentistWebAppBar(0, context)
          : AppBar(
              backgroundColor: AppColors.primary,
              leading: Builder(
                builder: (BuildContext context) {
                  return IconButton(
                    icon: const Icon(Icons.menu, color: Colors.white),
                    onPressed: () {
                      Scaffold.of(context).openDrawer();
                    },
                  );
                },
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: AppSizes.xs),
                  child: CustomImageView(
                    imagePath: AppIcons.notificationOutlined,
                    color: AppColors.white,
                    onTap: (token == null || token == '')
                        ? () {
                            AppUtils.showToastMessage('Please Sign in to Continue');
                            Get.to(() => const SigninOptionScreen());
                          }
                        : () {
                            Get.to(() => const NotificationScreen());
                          },
                  ),
                ),
                Obx(() => badges.Badge(
                  badgeContent: Text(
                    cartController.cartItems.length.toString(),
                    style: const TextStyle(color: Colors.white),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.shopping_cart, color: Colors.white),
                    onPressed: () => Get.to(() => const CartScreen()),
                  ),
                )),
                const SizedBox(width: AppSizes.md),
              ],
            ),
drawer: (kIsWeb && MediaQuery.of(context).size.width <= 600)
          ? Drawer(
              child: ListView(
                children: [
                  DrawerHeader(
                    decoration: const BoxDecoration(color: AppColors.primary),
                    child: CustomImageView(
                      fit: BoxFit.none,
                      imagePath: AppIcons.appLogo,
                      color: AppColors.white,
                    ),
                  ),
                  ListTile(
                    leading: CustomImageView(
                      imagePath: AppIcons.home,
                    ),
                    title: const Text('Home'),
                    onTap: () {
                      Get.offAll(() => const DentistHomeScreen());
                    },
                  ),
                  ListTile(
                    //leading: Icon(AppIcons.orders),
                    leading: CustomImageView(
                      imagePath: AppIcons.orders,
                    ),
                    title: const Text('Order'),
                    onTap: () {
                      Get.offAll(() => DentistOrdersScreen());
                    },
                  ),
                  ListTile(
                    //leading: Icon(AppIcons.orders),
                    leading: CustomImageView(
                      imagePath: AppIcons.reports,
                    ),
                    title: const Text('Reports'),
                    onTap: () {
                      Get.offAll(() => const DentistReportsScreen());
                    },
                  ),
                  ListTile(
                    //leading: Icon(AppIcons.orders),
                    leading: CustomImageView(
                      imagePath: AppIcons.profile,
                    ),
                    title: const Text('Profile'),
                    onTap: () {
                      Get.offAll(() => const DentistProfilescreen());
                    },
                  ),
                ],
              ),
            )
          :  const DentistDrawer(),
      body: GetBuilder<HomeController>(builder: (homeController) {
        if (homeController.isLoading) {
          return const Center(child: CircularProgressIndicator()); // ✅ Show loader only when loading
        }

        final dentalData = homeController.dentistHomeDataModel?.dentalLaboratory;
        final radiologyData = homeController.dentistHomeDataModel?.radiology;
        final materialSupplierData = homeController.dentistHomeDataModel?.materialSupplier;

        if (dentalData == null || dentalData.isEmpty) {
          return const Center(child: Text("No data available")); // ✅ Handle empty data case
        }
        return SafeArea(
          child: RefreshIndicator(
            onRefresh: () async {
              await homeController.getDashboard();
              await homeController.getDashboardData();
              storeController.fetchItems();

              setState(() {}); // Refresh the UI
            },
            child: SingleChildScrollView(
              child: Center(
                child: Column(
                  children: [
                    (token == null || token == '')
                        ? const SizedBox()
                        : userRecord?.isEmailVerified ?? false
                            ? const SizedBox()
                            : Padding(
                                padding: const EdgeInsets.all(AppSizes.md).copyWith(bottom: 0),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm2, vertical: AppSizes.sm),
                                  decoration: BoxDecoration(
                                    color: AppColors.white,
                                    boxShadow: AppDecoration.shadow1_3,
                                    borderRadius: BorderRadiusStyle.radius8,
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.info,
                                        color: AppColors.primary,
                                      ),
                                      const SizedBox(
                                        width: AppSizes.sm,
                                      ),
                                      Text(
                                        'Email verification is pending.',
                                        style: CustomTextStyles.b5,
                                      ),
                                      const Spacer(),
                                      isVerifyingEmail
                                          ? const CircularProgressIndicator()
                                          : IconButton(
                                              onPressed: () async {
                                                setState(() {
                                                  isVerifyingEmail = true;
                                                });
                                                final roleSelectionController = Get.find<RoleSelectionController>();
                                                await roleSelectionController.requestEmailOtp();
                                                setState(() {
                                                  isVerifyingEmail = false;
                                                });
                                              },
                                              icon: Text(
                                                'Verify',
                                                style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                              ))
                                    ],
                                  ),
                                ),
                              ),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Container(
                        decoration: BoxDecoration(
                          boxShadow: AppDecoration.shadow1_3,
                          borderRadius: BorderRadiusStyle.radius8,
                        ),
                        child: CustomSearchField<String>(
                          fetchItems: (query) => [],
                          hintText: "Search",
                          itemAsString: (item) => item,
                          onSelected: (item) {
                            log("Selected Item: $item");
                          },
                          onChanged: (query) async {
                            if (query.isNotEmpty) {
                              setState(() {
                                isSearching = true;
                              });

                              try {


                                  final List<Organization1> results = await searchControllerGet.organizationSearch(query);

                                setState(() {
                                  // Convert Organization to Organization1
                                  searchResults = results.map((org) => Organization1(
                                    id: org.id,
                                    name: org.name,
                                    file1: org.file1,
                                    address: org.address,
                                    mobile: org.mobile,
                                    email: org.email,
                                    description: org.description,
                                    organizationType: org.organizationType,
                                    organizationTypeId: org.organizationTypeId,
                                  )).toList();
                                });
                              } catch (e) {
                                log("Search API Error: $e");
                                setState(() {
                                  searchResults = [];
                                });
                              }
                            } else {
                              setState(() {
                                isSearching = false;
                                searchResults.clear();
                              });
                            }
                          },


                          // defaultItems: const ["Order 1", "Order 2", "Order 3"], // Optional
                        ),
                      ),
                    ),
                    //SizedBox(height: 10,),
                    isSearching
                        ? GridView.builder(
                            shrinkWrap: true,
                            physics:  const NeverScrollableScrollPhysics(),
                            // Prevents internal scrolling
                            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(context), // Responsive grid columns
                              mainAxisExtent: 130, // Adjust height as needed
                              mainAxisSpacing: 16,
                              crossAxisSpacing: 10,
                            ),
                            itemCount: searchResults.length,
                            itemBuilder: (context, index) {
                              final item = searchResults[index];
                              final serviceName = getServiceName(item.organizationType);


                              return GestureDetector(
                                onTap: () {
                                  Get.to(() => DentistServiceDetailsScreen(
                                        serviceId: item.id,
                                        serviceName: serviceName,
                                      ));
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      boxShadow: AppDecoration.shadow1_2,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    padding: const EdgeInsets.all(10),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        CustomImageView(
                                          imagePath: item.file1, // Ensure this path is correct
                                          width: 60,
                                          height: 60,
                                          fit: BoxFit.cover,
                                        ),
                                        const SizedBox(width: 10),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                item.name,
                                                style: CustomTextStyles.b2_1.copyWith(fontWeight: FontWeight.w600),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                item.description,
                                                style: CustomTextStyles.b6_3,
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                              const SizedBox(height: 10),
                                              Row(
                                                children: [
                                                  CustomImageView(
                                                    imagePath: AppIcons.location,
                                                    width: 14,
                                                    height: 14,
                                                  ),
                                                  const SizedBox(width: AppSizes.spaceExtraSmall),
                                                  Expanded(
                                                    child: Text(
                                                      item.address is List<String>
                                                          ? item.address.join(", ") // Convert List<String> to a single string
                                                          : item.address.toString(), // Convert String or other types to string
                                                      style: CustomTextStyles.b6_3,
                                                      maxLines: 2,
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          )
                        : Column(
                            children: [
                              (token == null || token == '')
                                  ? const SizedBox()
                                  : Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                      child: Container(
                                        height: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(AppSizes.md),
                                          border: Border.all(color: AppColors.primary.withOpacity(0.16), width: 0.4),
                                          boxShadow: [
                                            BoxShadow(
                                              blurRadius: 4,
                                              color: AppColors.primary.withOpacity(0.16),
                                            )
                                          ],
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                            children: [
                                              Text(
                                                'APPOINTMENTS FOR TODAY',
                                                style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                              ),
                                              Text(
                                                homeController.dentistHomeModel != null ? homeController.dentistHomeModel!.todaysAppointments.toString() : '9',
                                                style: CustomTextStyles.h5,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                              const SizedBox(height: AppSizes.md),
                              (token == null || token == '')
                                  ? const SizedBox()
                                  : Container(
                                      height: 240,
                                      decoration: BoxDecoration(color: Colors.white, borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(AppSizes.md), bottomRight: Radius.circular(AppSizes.md)), boxShadow: [BoxShadow(blurRadius: 4, color: AppColors.primary.withOpacity(0.16), offset: const Offset(0, 4))]),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Row(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              const Spacer(),
                                              GestureDetector(
                                                onTap: () {},
                                                child: Container(
                                                  height: 100,
                                                  width: MediaQuery.of(context).size.width < 600 ? MediaQuery.of(context).size.width * 0.43 : MediaQuery.of(context).size.width * 0.46,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius: BorderRadius.circular(AppSizes.md),
                                                    border: Border.all(color: AppColors.primary.withOpacity(0.16), width: 0.4),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        blurRadius: 4,
                                                        color: AppColors.primary.withOpacity(0.16),
                                                      )
                                                    ],
                                                  ),
                                                  child: Center(
                                                    child: Column(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Text(
                                                          homeController.dentistHomeModel != null ? homeController.dentistHomeModel!.monthlyRevenue.toString() : '50K',
                                                          style: CustomTextStyles.h5,
                                                        ),
                                                        const SizedBox(height: AppSizes.xs),
                                                        Padding(
                                                          padding: const EdgeInsets.only(left: 4),
                                                          child: Text(
                                                            'Monthly Revenue',
                                                            style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                                            maxLines: 2,
                                                            overflow: TextOverflow.ellipsis,
                                                            textAlign: TextAlign.center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: AppSizes.md),
                                              GestureDetector(
                                                onTap: () {},
                                                child: Container(
                                                  height: 100,
                                                  width: MediaQuery.of(context).size.width < 600 ? MediaQuery.of(context).size.width * 0.43 : MediaQuery.of(context).size.width * 0.46,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius: BorderRadius.circular(AppSizes.md),
                                                    border: Border.all(color: AppColors.primary.withOpacity(0.16), width: 0.4),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        blurRadius: 4,
                                                        color: AppColors.primary.withOpacity(0.16),
                                                      )
                                                    ],
                                                  ),
                                                  child: Center(
                                                    child: Column(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Text(
                                                          homeController.dentistHomeModel != null ? homeController.dentistHomeModel!.totalPatients.toString() : '50K',
                                                          style: CustomTextStyles.h5,
                                                        ),
                                                        const SizedBox(height: AppSizes.xs),
                                                        Padding(
                                                          padding: const EdgeInsets.all(8.0),
                                                          child: Text(
                                                            'No of Patients',
                                                            style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                                            maxLines: 2,
                                                            overflow: TextOverflow.ellipsis,
                                                            textAlign: TextAlign.center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const Spacer(),
                                            ],
                                          ),
                                          const SizedBox(height: AppSizes.md),
                                          Row(
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            children: [
                                              const Spacer(),
                                              GestureDetector(
                                                onTap: () {
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (context) => const DentistBottomBar(
                                                                initialIndex: 1,
                                                              )));
                                                },
                                                child: Container(
                                                  height: 100,
                                                  width: MediaQuery.of(context).size.width < 600 ? MediaQuery.of(context).size.width * 0.43 : MediaQuery.of(context).size.width * 0.46,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius: BorderRadius.circular(AppSizes.md),
                                                    border: Border.all(color: AppColors.primary.withOpacity(0.16), width: 0.4),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        blurRadius: 4,
                                                        color: AppColors.primary.withOpacity(0.16),
                                                      )
                                                    ],
                                                  ),
                                                  child: Center(
                                                    child: Column(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        // ✅ Add a null check before accessing `activeOrders`
                                                        Text(
                                                          homeController.dentistHomeModel != null ? homeController.dentistHomeModel!.activeOrders.toString() : '100', // Default value if null
                                                          style: CustomTextStyles.h5,
                                                        ),
                                                        const SizedBox(height: AppSizes.xs),
                                                        Padding(
                                                          padding: const EdgeInsets.only(left: 4),
                                                          child: Text(
                                                            'No of Active Orders',
                                                            style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                                            maxLines: 2,
                                                            overflow: TextOverflow.ellipsis,
                                                            textAlign: TextAlign.center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: AppSizes.md),
                                              GestureDetector(
                                                onTap: () {
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (context) => const DentistBottomBar(
                                                                initialIndex: 1,
                                                                initialOrderTab: 1,
                                                              )));
                                                },
                                                child: Container(
                                                  height: 100,
                                                  width: MediaQuery.of(context).size.width < 600 ? MediaQuery.of(context).size.width * 0.43 : MediaQuery.of(context).size.width * 0.46,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius: BorderRadius.circular(AppSizes.md),
                                                    border: Border.all(color: AppColors.primary.withOpacity(0.16), width: 0.4),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        blurRadius: 4,
                                                        color: AppColors.primary.withOpacity(0.16),
                                                      )
                                                    ],
                                                  ),
                                                  child: Center(
                                                    child: Column(
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Text(
                                                          homeController.dentistHomeModel != null ? homeController.dentistHomeModel!.completedPayableBills.toString() : '100',
                                                          style: CustomTextStyles.h5,
                                                        ),
                                                        const SizedBox(height: AppSizes.xs),
                                                        Padding(
                                                          padding: const EdgeInsets.all(8.0),
                                                          child: Text(
                                                            'No of Payable Bills',
                                                            style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                                            maxLines: 2,
                                                            overflow: TextOverflow.ellipsis,
                                                            textAlign: TextAlign.center,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const Spacer(),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                    (token == null || token == '')
                                        ? const SizedBox()
                                        : const SizedBox(
                                            height: AppSizes.defaultSpace,
                                          ),
                              //if(dentalData.isNotEmpty)
                              Container(
                                color: Colors.white,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Dental Labs',
                                            style: CustomTextStyles.b4_1,
                                          ),
                                          InkWell(
                                            onTap: () {
                                              Get.to(() => DentistServiceScreen(
                                                    serviceName: 'Dental Laboratory',
                                                    dataList: dentalData,
                                                    searchName: "laboratory",
                                                  ));
                                            },
                                            child: Text(
                                              'View all',
                                              style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: AppSizes.sm,
                                    ),
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          //height: 300 + AppSizes.md, // Adjust the height to accommodate the containers and padding
                                          height: MediaQuery.of(context).size.width > 1000
                                              ? 265
                                              : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                  ? 235
                                                  : 185,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            itemCount: dentalData.length,
                                            itemBuilder: (context, index) {
                                              final dentalLab = dentalData[index];
                                              return GestureDetector(
                                                onTap: () {
                                                  final String serviceId = dentalLab.id; // Get the service ID
                                                  print(serviceId);
                                                  Get.to(() => DentistServiceDetailsScreen(
                                                        serviceId: serviceId,
                                                        serviceName: "Dental Laboratory",
                                                      ));
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.only(
                                                    right: index == dentalData.length - 1 ? AppSizes.sm : 0,
                                                    left: 16,
                                                  ),
                                                  child: Column(
                                                    children: [
                                                      Container(
                                                        height: MediaQuery.of(context).size.width > 1000
                                                            ? 200
                                                            : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                                ? 170
                                                                : 120,
                                                        width: MediaQuery.of(context).size.width > 1000
                                                            ? Get.size.width * 0.25
                                                            : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                                ? Get.size.width * 0.35
                                                                : 174,
                                                        decoration: BoxDecoration(
                                                          borderRadius: const BorderRadius.only(
                                                            topLeft: Radius.circular(AppSizes.borderRadiusLg),
                                                            topRight: Radius.circular(AppSizes.borderRadiusLg),
                                                          ),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              blurRadius: 4,
                                                              color: AppColors.primary.withOpacity(0.16),
                                                            ),
                                                          ],
                                                        ),
                                                        child: ClipRRect(
                                                          borderRadius: const BorderRadius.only(
                                                            topLeft: Radius.circular(AppSizes.borderRadiusLg),
                                                            topRight: Radius.circular(AppSizes.borderRadiusLg),
                                                          ),
                                                          child: CustomImageView(
                                                            imagePath: dentalData[index].file1,
                                                          ),
                                                        ),
                                                      ),
                                                      Container(
                                                        height: 60,
                                                        width: MediaQuery.of(context).size.width > 1000
                                                            ? Get.size.width * 0.25
                                                            : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                                ? Get.size.width * 0.35
                                                                : 174,
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: const BorderRadius.only(
                                                            bottomLeft: Radius.circular(AppSizes.borderRadiusLg),
                                                            bottomRight: Radius.circular(AppSizes.borderRadiusLg),
                                                          ),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              blurRadius: 2,
                                                              color: AppColors.primary.withOpacity(0.16),
                                                              offset: const Offset(0, 2), // Shadow only on bottom
                                                            ),
                                                          ],
                                                        ),
                                                        child: Padding(
                                                          padding: const EdgeInsets.only(
                                                            left: AppSizes.sm,
                                                            right: AppSizes.sm,
                                                            top: AppSizes.sm,
                                                            bottom: AppSizes.sm2,
                                                          ),
                                                          child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Expanded(
                                                                child: Text(
                                                                  dentalLab.name,
                                                                  style: CustomTextStyles.b4,
                                                                ),
                                                              ),
                                                              // Expanded(
                                                              //   child: Text(dentalLab.description.toString(), style: CustomTextStyles.b6_3.copyWith(overflow: TextOverflow.ellipsis)),
                                                              // ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: AppSizes.spaceBtwItems,
                              ),
                              //if(radiologyData!.isNotEmpty)
                              Container(
                                color: Colors.white,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                                      //padding: const EdgeInsets.only(left: AppSizes.md,right: AppSizes.md,),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Radiology Centers',
                                            style: CustomTextStyles.b4_1,
                                          ),
                                          InkWell(
                                            onTap: () {
                                              Get.to(() => DentistServiceScreen(
                                                    serviceName: 'Radiology Centers',
                                                    dataList: radiologyData ?? [],
                                                    searchName: "RADIOLOGY",
                                                  ));
                                            },
                                            child: Text(
                                              'View all',
                                              style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(
                                      height: AppSizes.sm,
                                    ),
                                    radiologyData!.isEmpty? Container(
                                      height: 150,
                                      child: const Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [

                                          Center(child: Text("No Radiology Centers Available"),),
                                        ],
                                      ),
                                    ):

                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          //height: 300 + AppSizes.md, // Adjust the height to accommodate the containers and padding
                                          height: MediaQuery.of(context).size.width > 1000
                                              ? 265
                                              : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                  ? 235
                                                  : 185,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal, // Horizontal scrolling
                                            itemCount: radiologyData?.length, // The number of news items
                                            itemBuilder: (context, index) {
                                              final radiology = radiologyData?[index];
                                              return GestureDetector(
                                                onTap: () {
                                                  final String serviceId = radiology.id; // Get the service ID
                                                  print(serviceId);
                                                  Get.to(() => DentistServiceDetailsScreen(
                                                        serviceId: serviceId,
                                                        serviceName: 'Radiology Centers',
                                                      ));
                                                },
                                                child: Padding(
                                                  padding: EdgeInsets.only(
                                                    right: index == radiologyData!.length - 1 ? AppSizes.sm : 0, // Add right padding for the last item
                                                    left: 16,
                                                  ),
                                                  child: Column(
                                                    children: [
                                                      Container(
                                                        height: MediaQuery.of(context).size.width > 1000
                                                            ? 200
                                                            : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                                ? 170
                                                                : 120,
                                                        width: MediaQuery.of(context).size.width > 1000
                                                            ? Get.size.width * 0.25
                                                            : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                                ? Get.size.width * 0.35
                                                                : 174,
                                                        decoration: BoxDecoration(
                                                          borderRadius: const BorderRadius.only(
                                                            topLeft: Radius.circular(AppSizes.borderRadiusLg),
                                                            topRight: Radius.circular(AppSizes.borderRadiusLg),
                                                          ),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              blurRadius: 4,
                                                              color: AppColors.primary.withOpacity(0.16),
                                                            ),
                                                          ],
                                                        ),
                                                        child: ClipRRect(
                                                          borderRadius: const BorderRadius.only(
                                                            topLeft: Radius.circular(AppSizes.borderRadiusLg),
                                                            topRight: Radius.circular(AppSizes.borderRadiusLg),
                                                          ),
                                                          child: CustomImageView(
                                                            imagePath: radiologyData[index].file1,
                                                          ),
                                                        ),
                                                      ),
                                                      Container(
                                                        height: 60,
                                                        width: MediaQuery.of(context).size.width > 1000
                                                            ? Get.size.width * 0.25
                                                            : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                                                                ? Get.size.width * 0.35
                                                                : 174,
                                                        decoration: BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius: const BorderRadius.only(
                                                            bottomLeft: Radius.circular(AppSizes.borderRadiusLg),
                                                            bottomRight: Radius.circular(AppSizes.borderRadiusLg),
                                                          ),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              blurRadius: 2,
                                                              color: AppColors.primary.withOpacity(0.16),
                                                              offset: const Offset(0, 2), // Shadow only on bottom
                                                            ),
                                                          ],
                                                        ),
                                                        child: Padding(
                                                          padding: const EdgeInsets.only(
                                                            left: AppSizes.sm,
                                                            right: AppSizes.sm,
                                                            top: AppSizes.sm,
                                                            bottom: AppSizes.sm2,
                                                          ),
                                                          child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text(
                                                                radiology!.name,
                                                                style: CustomTextStyles.b4,
                                                                // maxLines: 1,
                                                                //overflow: TextOverflow.ellipsis,
                                                              ),
                                                              // const SizedBox(
                                                              //   height: AppSizes.xs,
                                                              // ),
                                                              // Expanded(
                                                              //   child: Text(radiology.description.toString(), style: CustomTextStyles.b6_3.copyWith(overflow: TextOverflow.ellipsis)),
                                                              // ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: AppSizes.spaceBtwItems,
                              ),
                              Container(
                                color: Colors.white,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: AppSizes.sm),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Platix Store',
                                            style: CustomTextStyles.b4_1,
                                          ),
                                          InkWell(
                                            onTap: () {
                                              Get.to(() => const StoreHomeScreen());
                                            },
                                            child: Text(
                                              'View all',
                                              style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    // const SizedBox(
                                    //   height: AppSizes.sm,
                                    // ),
                                    SizedBox(
                                      height: 235,
                                      child: Obx(() {
                                        if (storeController.featuredItems.isEmpty) {
                                          return const Center(child: Text("No featured items available"));
                                        }
                                        return ListView.builder(
                                          padding: const EdgeInsets.symmetric(vertical: AppSizes.sm),
                                          scrollDirection: Axis.horizontal,
                                          itemCount: storeController.featuredItems.length,
                                          itemBuilder: (context, index) {
                                            final item = storeController.featuredItems[index];
                                            return SizedBox(
                                              width: 174,
                                              child: Padding(
                                                padding: const EdgeInsets.only(left: 16.0),
                                                child: ItemCard(
                                                  item: item,
                                                  onTap: () {
                                                    Get.to(() => ItemDetailsScreen(item: item));
                                                  },
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      }),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                height: AppSizes.spaceBtwItems,
                              ),
                              //if(materialSupplierData!.isNotEmpty)
                              // Container(
                              //   color: Colors.white,
                              //   child: Column(
                              //     crossAxisAlignment: CrossAxisAlignment.start,
                              //     children: [
                              //       Padding(
                              //         padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                              //         //padding: const EdgeInsets.only(bottom: 8,left: AppSizes.md,right: AppSizes.md,top: 16),
                              //         child: Row(
                              //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              //           children: [
                              //             Text(
                              //               'Material Suppliers',
                              //               style: CustomTextStyles.b4_1,
                              //             ),
                              //             InkWell(
                              //               onTap: () {
                              //                 Get.to(() => DentistServiceScreen(
                              //                       serviceName: 'Material Suppliers',
                              //                       dataList: materialSupplierData ?? [],
                              //                       searchName: "material supplier",
                              //                     ));
                              //               },
                              //               child: Text(
                              //                 'View all',
                              //                 style: CustomTextStyles.b5.copyWith(color: AppColors.primary),
                              //               ),
                              //             ),
                              //           ],
                              //         ),
                              //       ),
                              //       const SizedBox(
                              //         height: AppSizes.sm,
                              //       ),
                              //       materialSupplierData!.isEmpty? const SizedBox(
                              //         height: 150,
                              //         child: Column(
                              //           crossAxisAlignment: CrossAxisAlignment.start,
                              //           mainAxisAlignment: MainAxisAlignment.center,
                              //           children: [
                              //             Center(child: Text("No Material Suppliers Available"),),
                              //           ],
                              //         ),
                              //       ):

                              //       Column(
                              //         mainAxisAlignment: MainAxisAlignment.center,
                              //         children: [
                              //           SizedBox(
                              //             //height: 300 + AppSizes.md,
                              //             height: MediaQuery.of(context).size.width > 1000
                              //                 ? 265
                              //                 : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                              //                     ? 235
                              //                     : 185,
                              //             child: ListView.builder(
                              //               scrollDirection: Axis.horizontal, // Horizontal scrolling
                              //               itemCount: materialSupplierData?.length, // The number of news items
                              //               itemBuilder: (context, index) {
                              //                 final material = materialSupplierData?[index];
                              //                 return GestureDetector(
                              //                   onTap: () {
                              //                     final String serviceId = material.id; // Get the service ID
                              //                     print(serviceId);
                              //                     Get.to(() => DentistServiceDetailsScreen(
                              //                           serviceId: serviceId,
                              //                           serviceName: 'Material Suppliers',
                              //                         ));
                              //                   },
                              //                   child: Padding(
                              //                     padding: EdgeInsets.only(
                              //                       right: index == materialSupplierData!.length - 1 ? AppSizes.sm : 0, // Add right padding for the last item
                              //                       left: 16,
                              //                     ),
                              //                     child: Column(
                              //                       children: [
                              //                         Container(
                              //                           height: MediaQuery.of(context).size.width > 1000
                              //                               ? 200
                              //                               : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                              //                                   ? 170
                              //                                   : 120,
                              //                           width: MediaQuery.of(context).size.width > 1000
                              //                               ? Get.size.width * 0.25
                              //                               : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                              //                                   ? Get.size.width * 0.35
                              //                                   : 174,
                              //                           decoration: BoxDecoration(
                              //                             borderRadius: const BorderRadius.only(
                              //                               topLeft: Radius.circular(AppSizes.borderRadiusLg),
                              //                               topRight: Radius.circular(AppSizes.borderRadiusLg),
                              //                             ),
                              //                             boxShadow: [
                              //                               BoxShadow(
                              //                                 blurRadius: 4,
                              //                                 color: AppColors.primary.withOpacity(0.16),
                              //                               ),
                              //                             ],
                              //                           ),
                              //                           child: ClipRRect(
                              //                             borderRadius: const BorderRadius.only(
                              //                               topLeft: Radius.circular(AppSizes.borderRadiusLg),
                              //                               topRight: Radius.circular(AppSizes.borderRadiusLg),
                              //                             ),
                              //                             child: CustomImageView(
                              //                               imagePath: materialSupplierData[index].file1,
                              //                             ),
                              //                           ),
                              //                         ),
                              //                         Container(
                              //                           height: 60,
                              //                           width: MediaQuery.of(context).size.width > 1000
                              //                               ? Get.size.width * 0.25
                              //                               : (MediaQuery.of(context).size.width < 1000 && MediaQuery.of(context).size.width > 600)
                              //                                   ? Get.size.width * 0.35
                              //                                   : 174,
                              //                           decoration: BoxDecoration(
                              //                             color: Colors.white,
                              //                             borderRadius: const BorderRadius.only(
                              //                               bottomLeft: Radius.circular(AppSizes.borderRadiusLg),
                              //                               bottomRight: Radius.circular(AppSizes.borderRadiusLg),
                              //                             ),
                              //                             boxShadow: [
                              //                               BoxShadow(
                              //                                 blurRadius: 2,
                              //                                 color: AppColors.primary.withOpacity(0.16),
                              //                                 offset: const Offset(0, 2), // Shadow only on bottom
                              //                               ),
                              //                             ],
                              //                           ),
                              //                           child: Padding(
                              //                             padding: const EdgeInsets.only(
                              //                               left: AppSizes.sm,
                              //                               right: AppSizes.sm,
                              //                               top: AppSizes.sm,
                              //                               bottom: AppSizes.sm2,
                              //                             ),
                              //                             child: Column(
                              //                               crossAxisAlignment: CrossAxisAlignment.start,
                              //                               children: [
                              //                                 Text(
                              //                                   material!.name,
                              //                                   maxLines: 1,
                              //                                   style: CustomTextStyles.b4,
                              //                                 ),
                              //                                 // Expanded(
                              //                                 //   child: Text(material.description.toString(), style: CustomTextStyles.b6_3.copyWith(overflow: TextOverflow.ellipsis)),
                              //                                 // ),
                              //                               ],
                              //                             ),
                              //                           ),
                              //                         ),
                              //                       ],
                              //                     ),
                              //                   ),
                              //                 );
                              //               },
                              //             ),
                              //           ),
                              //         ],
                              //       )
                              //     ],
                              //   ),
                              // ),
                              // const SizedBox(
                              //   height: AppSizes.spaceBtwItems,
                              // ),
                            ],
                          ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
    }
    );
  }


  String getServiceName(String organizationType) {
    switch (organizationType) {
      case 'Radiology':
        return 'Radiology Center';
      case 'Dental Laboratory':
        return 'Dental Laboratory';
      case 'Material Supplier':
        return 'Material Supplier';
      default:
        return 'Unknown Service';
    }
  }
}
