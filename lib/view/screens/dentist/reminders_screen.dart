import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/create_reminder_screen.dart';

class RemindersScreen extends StatelessWidget {
  const RemindersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PermissionService _permissionService = PermissionService();
    return Visibility(
      visible: _permissionService.hasAnyPermission('remainders', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Reminders'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create Reminders',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                Visibility(
                  visible: _permissionService.hasPermission('remainders', 'is_list'),
                  child: DataTable(
                    columnSpacing: 10,
                    horizontalMargin: 10,
                    headingRowColor: MaterialStateProperty.all(AppColors.primary),
                    headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    columns: const [
                      DataColumn(label: Text('S.No')),
                      DataColumn(label: Text('Patient Name')),
                      DataColumn(label: Text('Date')),
                      DataColumn(label: Text('Edit')),
                      DataColumn(label: Text('Delete')),
                    ],
                    rows: [
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          const DataCell(Text('1')),
                          const DataCell(Text('Matthew Thomas')),
                          const DataCell(Text('30/10/2020')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(Colors.white),
                        cells: [
                          const DataCell(Text('2')),
                          const DataCell(Text('John Davis')),
                          const DataCell(Text('16/10/2021')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          const DataCell(Text('3')),
                          const DataCell(Text('Elizabeth Jones')),
                          const DataCell(Text('10/01/2024')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(Colors.white),
                        cells: [
                          const DataCell(Text('4')),
                          const DataCell(Text('Ryan Young')),
                          const DataCell(Text('09/01/2022')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          const DataCell(Text('5')),
                          const DataCell(Text('Anthony Moore')),
                          const DataCell(Text('30/01/2025')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(Colors.white),
                        cells: [
                          const DataCell(Text('6')),
                          const DataCell(Text('Nicole Davis')),
                          const DataCell(Text('23/01/2024')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                      DataRow(
                        color: MaterialStateProperty.all(AppColors.primary2),
                        cells: [
                          const DataCell(Text('7')),
                          const DataCell(Text('Stepane Johnson')),
                          const DataCell(Text('19/02/2024')),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_edit'),
                              child: IconButton(icon: const Icon(Icons.edit, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                          DataCell(
                            Visibility(
                              visible: _permissionService.hasPermission('remainders', 'is_delete'),
                              child: IconButton(icon: const Icon(Icons.delete, color: AppColors.primary), onPressed: () {}),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: _permissionService.hasPermission('remainders', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreateReminderScreen());
            },
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }
}
