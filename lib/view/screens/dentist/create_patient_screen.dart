import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/custom_text_form_field.dart';

class CreatePatientScreen extends StatelessWidget {
  const CreatePatientScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Patient'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'First Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter First Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Last Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Last Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Mobile Number',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: '+91 XXXXXXXXXX',
                prefix: Icon(Icons.phone),
              ),
              const SizedBox(height: 20),
              const Text(
                'Email',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: '<EMAIL>',
                prefix: Icon(Icons.email),
              ),
              const SizedBox(height: 20),
              const Text(
                'Gender',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  Radio(
                    value: 'Male',
                    groupValue: 'Male',
                    onChanged: (value) {},
                  ),
                  const Text('Male'),
                  Radio(
                    value: 'Female',
                    groupValue: 'Male',
                    onChanged: (value) {},
                  ),
                  const Text('Female'),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Age',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '10',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date Of Birth',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '09 Feb 2021',
                          prefix: Icon(Icons.calendar_today),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Address',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Address',
              ),
              const SizedBox(height: 20),
              const Text(
                'City',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Select City',
                suffix: Icon(Icons.arrow_drop_down),
              ),
              const SizedBox(height: 20),
              const Text(
                'State',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Select State',
                suffix: Icon(Icons.arrow_drop_down),
              ),
              const SizedBox(height: 30),
              CustomElevatedButton(
                onPressed: () {},
                text: 'Save',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
