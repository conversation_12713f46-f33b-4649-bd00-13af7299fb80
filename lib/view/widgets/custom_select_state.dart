library country_state_city_picker_nona;

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:dropdown_search/dropdown_search.dart';
import 'package:platix/models/select_status_model.dart' as StatusModel;
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/textfield_label.dart';

class CustomSelectState extends StatefulWidget {
  final ValueChanged<String> onCountryChanged;
  final ValueChanged<String> onStateChanged;
  final ValueChanged<String> onCityChanged;
  final VoidCallback? onCountryTap;
  final VoidCallback? onStateTap;
  final VoidCallback? onCityTap;
  final String? defaultCountry;
  final TextStyle? style;
  final Color? dropdownColor;
  final InputDecoration decoration;
  final double spacing;

  const CustomSelectState(
      {Key? key,
      required this.onCountryChanged,
      required this.onStateChanged,
      required this.onCityChanged,
      this.defaultCountry,
      this.decoration =
          const InputDecoration(contentPadding: EdgeInsets.all(0.0)),
      this.spacing = 0.0,
      this.style,
      this.dropdownColor,
      this.onCountryTap,
      this.onStateTap,
      this.onCityTap})
      : super(key: key);

  @override
  _CustomSelectStateState createState() => _CustomSelectStateState();
}

class _CustomSelectStateState extends State<CustomSelectState> {
  List<String> _cities = ["Choose City"];
  List<String> _country = ["Choose Country"];
  String _selectedCity = "Choose City";
  String _selectedCountry = "Choose Country";
  String _selectedState = "Choose State/Province";
  List<String> _states = ["Choose State/Province"];
  var responses;

  @override
  void initState() {
    if (widget.defaultCountry != null) {
      _selectedCountry = widget.defaultCountry!;
    }
    getCounty().then((_) {
      if (widget.defaultCountry != null) {
        getState();
      }
    });
    super.initState();
  }

  Future getResponse() async {
    var res = await rootBundle.loadString(
        'packages/country_state_city_picker/lib/assets/country.json');
    return jsonDecode(res);
  }

  Future getCounty() async {
    var countryres = await getResponse() as List;
    countryres.forEach((data) {
      var model = StatusModel.StatusModel();
      model.name = data['name'];
      model.emoji = data['emoji'];
      if (!mounted) return;
      setState(() {
        _country.add(model.emoji! + "    " + model.name!);
      });
    });

    return _country;
  }

  Future getState() async {
    var response = await getResponse();
    var takestate = response
        .map((map) => StatusModel.StatusModel.fromJson(map))
        .where((item) => item.emoji + "    " + item.name == _selectedCountry)
        .map((item) => item.state)
        .toList();
    var states = takestate as List;
    states.forEach((f) {
      if (!mounted) return;
      setState(() {
        var name = f.map((item) => item.name).toList();
        for (var statename in name) {
          print(statename.toString());

          _states.add(statename.toString());
        }
      });
    });

    return _states;
  }

  Future getCity() async {
    var response = await getResponse();
    var takestate = response
        .map((map) => StatusModel.StatusModel.fromJson(map))
        .where((item) => item.emoji + "    " + item.name == _selectedCountry)
        .map((item) => item.state)
        .toList();
    var states = takestate as List;
    states.forEach((f) {
      var name = f.where((item) => item.name == _selectedState);
      var cityname = name.map((item) => item.city).toList();
      cityname.forEach((ci) {
        if (!mounted) return;
        setState(() {
          var citiesname = ci.map((item) => item.name).toList();
          for (var citynames in citiesname) {
            print(citynames.toString());

            _cities.add(citynames.toString());
          }
        });
      });
    });
    return _cities;
  }

  void _onSelectedCountry(String value) {
    if (!mounted) return;
    setState(() {
      _selectedState = "Choose  State/Province";
      _states = ["Choose  State/Province"];
      _selectedCountry = value;
      this.widget.onCountryChanged(value);
      getState();
    });
  }

  void _onSelectedState(String value) {
    if (!mounted) return;
    setState(() {
      _selectedCity = "Choose City";
      _cities = ["Choose City"];
      _selectedState = value;
      this.widget.onStateChanged(value);
      getCity();
    });
  }

  void _onSelectedCity(String value) {
    if (!mounted) return;
    setState(() {
      _selectedCity = value;
      this.widget.onCityChanged(value);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFieldLabel(label: 'Choose State/Province'),
            Container(
              decoration: BoxDecoration(
                boxShadow: AppDecoration.shadow1_3,
                color: Colors.white,
                borderRadius: BorderRadiusStyle.border12,
              ),
              child: DropdownSearch<String>(
                items: _states,
                dropdownBuilder: (context, selectedItem) {
                  return Container(
                      child: selectedItem != null
                          ? Text(
                              selectedItem,
                              style: TextStyle(
                                color: Color(0xff0F1031),
                                fontSize: 11,
                              ),
                            )
                          : null);
                },
                popupProps: PopupProps.menu(
                  disabledItemFn: (value) => value == "Choose  State/Province",
                  showSearchBox: true,
                  searchFieldProps: TextFieldProps(autofocus: true),
                  // showSelectedItems: true,
                ),
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    hintText: "Choose State/Province",
                    hintStyle:
                        CustomTextStyles.b5.copyWith(color: AppColors.darkGrey),
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: 18, horizontal: AppSizes.md),
                    fillColor: Colors.white,
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: const BorderSide(
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: BorderSide(
                        color: Colors.transparent,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: BorderSide(
                        color: AppColors.primary,
                        width: 1,
                      ),
                    ),
                  ),
                ),
                onChanged: (value) => _onSelectedState(value!),
              ),
            ),
          ],
        ),
        SizedBox(
          height: widget.spacing,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFieldLabel(label: 'Choose City'),
            Container(
              decoration: BoxDecoration(
                boxShadow: AppDecoration.shadow1_3,
                color: Colors.white,
                borderRadius: BorderRadiusStyle.border12,
              ),
              child: DropdownSearch<String>(
                items: _cities,
                dropdownBuilder: (context, selectedItem) {
                  return Container(
                      child: selectedItem != null
                          ? Text(
                              selectedItem,
                              style: TextStyle(
                                color: Color(0xff0F1031),
                                fontSize: 11,
                              ),
                            )
                          : null);
                },
                popupProps: PopupProps.menu(
                  disabledItemFn: (value) => value == "Choose City",
                  showSearchBox: true,
                  searchFieldProps: TextFieldProps(autofocus: true),
                  // showSelectedItems: true,
                ),
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    hintText: "Choose City",
                    hintStyle:
                        CustomTextStyles.b5.copyWith(color: AppColors.darkGrey),
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: 18, horizontal: AppSizes.md),
                    fillColor: Colors.white,
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: const BorderSide(
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: BorderSide(
                        color: Colors.transparent,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: BorderSide(
                        color: AppColors.primary,
                        width: 1,
                      ),
                    ),
                  ),
                ),
                onChanged: (value) => _onSelectedCity(value!),
              ),
            ),
          ],
        ),
        SizedBox(
          height: widget.spacing,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFieldLabel(label: 'Choose Country'),
            Container(
              decoration: BoxDecoration(
                boxShadow: AppDecoration.shadow1_3,
                color: Colors.white,
                borderRadius: BorderRadiusStyle.border12,
              ),
              child: DropdownSearch<String>(
                selectedItem: _selectedCountry,
                items: _country,
                dropdownBuilder: (context, selectedItem) {
                  return Container(
                      child: selectedItem != null
                          ? Text(
                              selectedItem,
                              style: TextStyle(
                                color: Color(0xff0F1031),
                                fontSize: 11,
                              ),
                            )
                          : Text(
                              _selectedCountry,
                              style: TextStyle(
                                color: Color(0xff0F1031),
                                fontSize: 11,
                              ),
                            ));
                },
                popupProps: PopupProps.menu(
                  disabledItemFn: (value) => value == "Choose Country",
                  showSearchBox: true,
                  searchFieldProps: TextFieldProps(autofocus: true),
                  // showSelectedItems: true,
                ),
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    hintText: "Choose Country",
                    hintStyle:
                        CustomTextStyles.b5.copyWith(color: AppColors.darkGrey),
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                        vertical: 18, horizontal: AppSizes.md),
                    fillColor: Colors.white,
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: const BorderSide(
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: BorderSide(
                        color: Colors.transparent,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadiusStyle.border12,
                      borderSide: BorderSide(
                        color: AppColors.primary,
                        width: 1,
                      ),
                    ),
                  ),
                ),
                onChanged: (value) => _onSelectedCountry(value!),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
