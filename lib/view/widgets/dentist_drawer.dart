import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/appointments_screen.dart';
import 'package:platix/view/screens/dentist/billing_screen.dart';
import 'package:platix/view/screens/dentist/dashboard_screen.dart';
import 'package:platix/view/screens/dentist/edr_screen.dart';
import 'package:platix/view/screens/dentist/reminders_screen.dart';
import 'package:platix/view/screens/dentist/reports_screen.dart';
import 'package:platix/view/screens/dentist/dentist_homescreen.dart';
import 'package:platix/view/screens/dentist/dentist_bottombar.dart'; // Import DentistBottomBar

class DentistDrawer extends StatefulWidget {
  const DentistDrawer({super.key});

  @override
  State<DentistDrawer> createState() => _DentistDrawerState();
}

class _DentistDrawerState extends State<DentistDrawer> {
  String selectedItem = 'Home';

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            padding: EdgeInsets.zero,
            decoration: const BoxDecoration(color: AppColors.primary),
            child: CustomImageView(
              fit: BoxFit.none,
              imagePath: AppIcons.appLogo,
              color: AppColors.white,
            ),
          ),
          _buildDrawerItem(
            icon: Icons.home,
            title: 'Home',
            onTap: () {
              Get.to(() => const DentistHomeScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.dashboard,
            title: 'Dashboard',
            onTap: () {
              Get.to(() => const DashboardScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.calendar_today,
            title: 'Appointments',
            onTap: () {
              Get.to(() => const AppointmentsScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.person_add,
            title: 'Registration',
            onTap: () {
              Get.toNamed(AppRoutes.patientRegistrationScreen);
            },
          ),
          _buildDrawerItem(
            icon: Icons.receipt,
            title: 'Billing',
            onTap: () {
              Get.to(() => const BillingScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.description,
            title: 'EDR',
            onTap: () {
              Get.to(() => const EdrScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.biotech,
            title: 'Lab Records',
            onTap: () {
              Get.to(() => const DentistBottomBar(
                  initialIndex: 1)); // Navigate to DentistBottomBar with Orders tab selected
            },
          ),
          _buildDrawerItem(
            icon: Icons.image,
            title: 'Imaging Orders',
            onTap: () {
              Get.to(() => const DentistBottomBar(
                  initialIndex: 1)); // Navigate to DentistBottomBar with Orders tab selected
            },
          ),
          _buildDrawerItem(
            icon: Icons.notifications,
            title: 'Reminders',
            onTap: () {
              Get.to(() => const RemindersScreen());
            },
          ),
          _buildDrawerItem(
            icon: Icons.bar_chart,
            title: 'Reports',
            onTap: () {
              Get.to(() => const ReportsScreen());
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final isSelected = selectedItem == title;
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Theme.of(context).primaryColor : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Theme.of(context).primaryColor : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      onTap: () {
        setState(() {
          selectedItem = title;
        });
        onTap();
      },
      selected: isSelected,
      selectedTileColor: Theme.of(context).primaryColor.withOpacity(0.1),
    );
  }
}
