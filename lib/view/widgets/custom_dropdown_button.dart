import 'package:flutter/material.dart';

class CustomDropdownButton extends StatelessWidget {
  final String hintText;
  final List<DropdownMenuItem<String>> items;
  final String? value;
  final ValueChanged<String?>? onChanged;
  final IconData? prefixIcon;

  const CustomDropdownButton({
    super.key,
    required this.hintText,
    required this.items,
    this.value,
    this.onChanged,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        hintText: hintText,
        prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
      value: value,
      isExpanded: true,
      items: items,
      onChanged: onChanged,
    );
  }
}
