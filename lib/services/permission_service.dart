import 'package:get_storage/get_storage.dart';
import 'package:flutter/material.dart';

class PermissionService {
  final GetStorage _box = GetStorage();

  bool hasPermission(String route, String permissionType) {
    final permissions = _box.read('user_permissions');
    if (permissions == null || permissions is! List) {
      print('⚠️ PermissionService: No permissions found or invalid format');
      return false;
    }

    final routePermission = permissions.firstWhere(
      (p) => p['route'] == route,
      orElse: () => null,
    );

    if (routePermission == null) {
      print('⚠️ PermissionService: Route "$route" not found in permissions');
      print('📋 Available routes: ${permissions.map((p) => p['route']).join(', ')}');
      return false;
    }

    final permissionFlags = routePermission['permissions'];
    if (permissionFlags == null || permissionFlags is! Map) {
      print('⚠️ PermissionService: No permission flags found for route "$route"');
      return false;
    }

    final permissionValue = permissionFlags[permissionType];

    // Handle different types of permission values
    if (permissionValue == null) {
      print('⚠️ PermissionService: Permission "$permissionType" not found for route "$route"');
      print('📋 Available permissions: ${permissionFlags.keys.join(', ')}');
      return false;
    } else if (permissionValue is bool) {
      return permissionValue;
    } else if (permissionValue is String) {
      // Convert string to boolean
      final result = permissionValue.toLowerCase() == 'true' || permissionValue == '1';
      print('✅ PermissionService: $route.$permissionType = "$permissionValue" → $result');
      return result;
    } else if (permissionValue is int) {
      // Convert int to boolean (1 = true, 0 = false)
      final result = permissionValue == 1;
      print('✅ PermissionService: $route.$permissionType = $permissionValue → $result');
      return result;
    } else {
      print('⚠️ PermissionService: Unexpected permission value type for $route.$permissionType: ${permissionValue.runtimeType}');
      return false;
    }
  }

  /// Check if user has any of the specified permissions for a route
  bool hasAnyPermission(String route, List<String> permissionTypes) {
    return permissionTypes.any((permissionType) => hasPermission(route, permissionType));
  }

  /// Debug method to print all available permissions
  void debugPrintPermissions() {
    final permissions = _box.read('user_permissions');
    if (permissions == null || permissions is! List) {
      print('🔍 PermissionService Debug: No permissions found');
      return;
    }

    print('🔍 PermissionService Debug: Available permissions:');
    for (var permission in permissions) {
      final route = permission['route'];
      final flags = permission['permissions'];
      print('  📁 Route: $route');
      if (flags is Map) {
        flags.forEach((key, value) {
          print('    🔑 $key: $value (${value.runtimeType})');
        });
      }
      print('');
    }
  }
}

/*
  EXAMPLE USAGE:

  final PermissionService _permissionService = PermissionService();

  // In your UI code:
  Visibility(
    visible: _permissionService.hasPermission('billing', 'is_view'),
    child: YourBillingWidget(),
  )

  // In your navigation logic:
  if (_permissionService.hasPermission('billing', 'is_view')) {
    // Navigate to billing screen
  } else {
    // Show an error or redirect
  }
*/
