class RolePermissionModel {
  final String id;
  final String roleName;
  final List<String> permissions;

  RolePermissionModel({
    required this.id,
    required this.roleName,
    required this.permissions,
  });

  factory RolePermissionModel.fromJson(Map<String, dynamic> json) {
    return RolePermissionModel(
      id: json['_id'],
      roleName: json['roleName'],
      permissions: List<String>.from(json['permissions']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'roleName': roleName,
      'permissions': permissions,
    };
  }
}
