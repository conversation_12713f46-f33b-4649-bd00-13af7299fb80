{"buildFiles": ["C:\\src\\flutter_windows_3.24.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\innoitlabs\\platix\\platix_app\\android\\app\\.cxx\\Debug\\1c582o55\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\innoitlabs\\platix\\platix_app\\android\\app\\.cxx\\Debug\\1c582o55\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}